"use client"
import { useState } from 'react'
import Link from "next/link"
import { HeartIcon } from "@/components/ui/heart-icon"
import { trackContactForm } from "@/components/analytics"
import { submitContactForm } from "@/lib/api"
import { Footer } from "@/components/ui/footer"

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    type: 'general'
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError('')

    try {
      // Submit to backend API with fallback to localStorage
      const response = await submitContactForm(formData)

      if (response.success) {
        setIsSubmitted(true)
        setFormData({ name: '', email: '', subject: '', message: '', type: 'general' })

        // Track contact form submission
        trackContactForm(formData.type)
      } else {
        setError(response.error || 'Something went wrong. Please try again.')
      }
    } catch (err) {
      setError('Something went wrong. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  return (
    <div className="min-h-screen relative overflow-hidden bg-transparent">


      {/* Header */}
      <header className="relative z-10 w-full px-4 py-6 border-b border-white/20">
        <div className="max-w-6xl mx-auto">
          <nav className="flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center shadow-lg border border-white/30 transform group-hover:scale-110 transition-all duration-300">
              <HeartIcon className="w-7 h-7 text-white" />
            </div>
            <span className="text-3xl font-bold text-white drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>Vierla</span>
          </Link>
          <div className="flex items-center space-x-8">
            <Link href="/" className="text-white/90 hover:text-white transition-colors">Home</Link>
            <Link href="/about" className="text-white/90 hover:text-white transition-colors">About</Link>
          </div>
        </nav>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 w-full px-4 py-16" style={{background: 'linear-gradient(to bottom right, #364035, #8B9A8C, #364035)', backgroundSize: '100% 100%', backgroundRepeat: 'no-repeat'}}>
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6" style={{fontFamily: 'Playfair Display, serif'}}>
              Contact Us
            </h1>
            <p className="text-xl text-white/90 max-w-2xl mx-auto">
              Have questions about our services? Want to join our platform as a beauty professional? We'd love to hear from you.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="bg-white/10 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/20">
              <h2 className="text-2xl font-bold text-white mb-6" style={{fontFamily: 'Playfair Display, serif'}}>
                Send us a message
              </h2>
              
              {isSubmitted ? (
                <div className="text-center py-8">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-4" style={{backgroundColor: 'rgba(244, 241, 232, 0.2)'}}>
                    <svg className="w-8 h-8" style={{color: '#F4F1E8'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">Message Sent!</h3>
                  <p className="text-white/80">Thank you for reaching out. We'll get back to you within 24 hours.</p>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-white/90 text-sm font-medium mb-2">Name *</label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                        placeholder="Your full name"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-white/90 text-sm font-medium mb-2">Email *</label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">I'm interested in *</label>
                    <select
                      name="type"
                      value={formData.type}
                      onChange={handleChange}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white focus:outline-none focus:ring-2 focus:ring-white/50 appearance-none cursor-pointer"
                      style={{
                        backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`,
                        backgroundPosition: 'right 0.75rem center',
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: '1.5em 1.5em'
                      }}
                      required
                    >
                      <option value="general" style={{backgroundColor: '#2D2A26', color: '#FFFFFF'}}>General inquiry</option>
                      <option value="customer" style={{backgroundColor: '#2D2A26', color: '#FFFFFF'}}>Booking services</option>
                      <option value="professional" style={{backgroundColor: '#2D2A26', color: '#FFFFFF'}}>Joining as a professional</option>
                      <option value="partnership" style={{backgroundColor: '#2D2A26', color: '#FFFFFF'}}>Business partnership</option>
                      <option value="support" style={{backgroundColor: '#2D2A26', color: '#FFFFFF'}}>Technical support</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Subject *</label>
                    <input
                      type="text"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="Brief subject line"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Message *</label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      rows={5}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none"
                      placeholder="Tell us more about your inquiry..."
                      required
                    />
                  </div>
                  
                  {error && (
                    <p className="text-red-300 text-sm">{error}</p>
                  )}
                  
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full py-3 rounded-xl font-medium transition-all duration-300 disabled:opacity-50 hover:scale-105"
                    style={{backgroundColor: '#B8956A', color: '#2D2A26'}}
                  >
                    {isSubmitting ? 'Sending...' : 'Send Message'}
                  </button>
                </form>
              )}
            </div>

            {/* Contact Information */}
            <div className="space-y-8">
              <div className="bg-white/10 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/20">
                <h2 className="text-2xl font-bold text-white mb-6" style={{fontFamily: 'Playfair Display, serif'}}>
                  Get in Touch
                </h2>
                
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 rounded-xl flex items-center justify-center" style={{backgroundColor: 'rgba(244, 241, 232, 0.2)'}}>
                      <svg className="w-6 h-6" style={{color: '#F4F1E8'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-1">Email</h3>
                      <p className="text-white/80"><EMAIL></p>
                      <p className="text-white/60 text-sm">We typically respond within 24 hours</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 rounded-xl flex items-center justify-center" style={{backgroundColor: 'rgba(244, 241, 232, 0.2)'}}>
                      <svg className="w-6 h-6" style={{color: '#F4F1E8'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-1">Service Areas</h3>
                      <p className="text-white/80">Currently expanding nationwide</p>
                      <p className="text-white/60 text-sm">Starting with major metropolitan areas</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 rounded-xl flex items-center justify-center" style={{backgroundColor: 'rgba(244, 241, 232, 0.2)'}}>
                      <svg className="w-6 h-6" style={{color: '#F4F1E8'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-1">Response Time</h3>
                      <p className="text-white/80">Within 24 hours</p>
                      <p className="text-white/60 text-sm">Monday - Friday, 9 AM - 6 PM EST</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/20">
                <h3 className="text-xl font-bold text-white mb-4" style={{fontFamily: 'Playfair Display, serif'}}>
                  For Beauty Professionals
                </h3>
                <p className="text-white/80 mb-4">
                  Interested in joining our platform? We're always looking for talented, licensed beauty professionals.
                </p>
                <Link
                  href="/about"
                  className="inline-flex items-center px-4 py-2 rounded-xl border border-white/30 text-white hover:bg-white/10 transition-all duration-300"
                >
                  Learn More
                  <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />


    </div>
  )
}
