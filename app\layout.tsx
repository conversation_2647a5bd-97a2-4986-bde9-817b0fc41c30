import type { Metadata } from 'next'
import './globals.css'
import { StructuredData } from '@/components/structured-data'
import { PerformanceMonitor } from '@/components/performance-monitor'
import { Analytics } from '@/components/analytics'
import { PerformanceDashboard } from '@/components/performance-dashboard'
import { BackendStatusIndicator } from '@/components/backend-status'
import { TestingSuite } from '@/components/testing-suite'
import { CookieConsent } from '@/components/ui/cookie-consent'
import { LocalSEO } from '@/components/local-seo'
import { ServiceWorkerRegistration } from '@/components/service-worker'

export const metadata: Metadata = {
  title: 'Vierla - Self-Care, Simplified | Toronto & Ottawa Beauty Services',
  description: 'Book top-vetted hair stylists, makeup artists, and nail technicians in Toronto and Ottawa. Professional beauty services at your place or theirs. Ontario\'s premier beauty marketplace.',
  generator: 'Next.js',
  metadataBase: new URL('https://vierla.com'),
  alternates: {
    canonical: '/',
  },
  keywords: [
    'beauty services Toronto',
    'hair stylist Toronto',
    'makeup artist Toronto',
    'nail technician Toronto',
    'beauty services Ottawa',
    'hair stylist Ottawa',
    'makeup artist Ottawa',
    'nail technician Ottawa',
    'mobile beauty services',
    'in-home beauty services',
    'Ontario beauty marketplace',
    'professional beauty services',
    'vetted beauty professionals',
    'beauty booking platform'
  ],
  other: {
    'X-Frame-Options': 'SAMEORIGIN',
    'X-Content-Type-Options': 'nosniff',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
  },
  openGraph: {
    title: 'Vierla - Self-Care, Simplified | Toronto & Ottawa Beauty Services',
    description: 'Connect with top-vetted beauty professionals in Toronto & Ottawa. Hair styling, makeup, nails, and more - at your place or theirs. Ontario\'s premier beauty marketplace.',
    url: 'https://vierla.com',
    siteName: 'Vierla',
    images: [
      {
        url: '/og-image.svg',
        width: 1200,
        height: 630,
        alt: 'Vierla - Self-Care, Simplified. Beauty services in Toronto & Ottawa.',
      },
    ],
    locale: 'en_CA',
    type: 'website',
    countryName: 'Canada',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Vierla - Self-Care, Simplified',
    description: 'Connect with top-vetted beauty professionals in Toronto & Ottawa. Hair styling, makeup, nails, and more - at your place or theirs. Professional applications opening soon.',
    images: ['/og-image.svg'],
    creator: '@vierla',
    site: '@vierla',
  },
  icons: {
    icon: [
      { url: '/favicon.svg', type: 'image/svg+xml' },
    ],
    shortcut: '/favicon.svg',
    apple: '/favicon.svg',
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
        <meta name="theme-color" content="#364035" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="mobile-web-app-capable" content="yes" />
        {/* Performance optimizations */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="https://www.google-analytics.com" />
        <link rel="dns-prefetch" href="https://www.googletagmanager.com" />

        {/* App icons and manifest */}
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />

        {/* Fonts with performance optimization */}
        <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700;900&family=Manrope:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
        <style>{`
          @font-face {
            font-family: 'Playfair Display';
            font-display: swap;
          }
          @font-face {
            font-family: 'Manrope';
            font-display: swap;
          }
          /* Prevent layout shift and FOUC */
          html, body {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
          }
          .mobile-full-height {
            min-height: 100vh;
            min-height: -webkit-fill-available;
          }
          .loading {
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
          }
          .loaded {
            opacity: 1;
          }
          /* Improve performance */
          * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
          }
        `}</style>
      </head>
      <body
        style={{
          fontFamily: 'Manrope, sans-serif',
          background: 'linear-gradient(to bottom right, #364035, #8B9A8C, #364035)',
          backgroundAttachment: 'fixed',
          minHeight: '100vh',
          width: '100%',
          overflowX: 'hidden',
          margin: 0,
          padding: 0
        }}
        className="mobile-full-height"
        role="document"
      >
        <StructuredData />
        <PerformanceMonitor />
        <Analytics />
        <PerformanceDashboard />
        <BackendStatusIndicator />
        <TestingSuite />
        <CookieConsent />
        <LocalSEO />
        <ServiceWorkerRegistration />
        {children}
      </body>
    </html>
  )
}
