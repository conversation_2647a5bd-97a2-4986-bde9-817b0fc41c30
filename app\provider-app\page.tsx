'use client'

import Link from "next/link"
import { HeartIcon } from "@heroicons/react/24/outline"
import { Footer } from "@/components/ui/footer"

export default function ProviderAppPage() {
  return (
    <div className="min-h-screen relative overflow-hidden bg-transparent">


      {/* Header */}
      <header className="relative z-20 w-full px-4 py-6 border-b border-white/20">
        <div className="max-w-6xl mx-auto">
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center shadow-lg border border-white/30 transform group-hover:scale-110 transition-all duration-300">
              <HeartIcon className="w-7 h-7 text-white" />
            </div>
            <span className="text-3xl font-bold text-white drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>V<PERSON><PERSON></span>
          </Link>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            
            {/* Hero Section */}
            <div className="mb-16">
              <div className="inline-flex items-center rounded-full px-6 py-3 mb-8 shadow-lg border-2" style={{backgroundColor: 'rgba(184, 149, 106, 0.2)', borderColor: '#B8956A'}}>
                <span className="text-white font-medium drop-shadow-sm">For Service Providers</span>
              </div>

              <h1 className="text-5xl md:text-7xl font-black mb-8 leading-none text-white drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>
                <span className="inline-block" style={{color: '#F4F1E8'}}>
                  The All-In-One Tool to Run and Grow Your Business
                </span>
              </h1>

              <p className="text-xl md:text-2xl text-white/90 mb-12 leading-relaxed font-light max-w-3xl mx-auto drop-shadow-sm">
                Manage your calendar, get discovered by new clients, and grow your beauty business with powerful tools designed for professionals.
              </p>
            </div>

            {/* Key Benefits */}
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
              {[
                {
                  icon: (
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a4 4 0 118 0v4m-4 8a4 4 0 11-8 0v-4m4-4h8m-4-4v8" />
                    </svg>
                  ),
                  title: "Manage Your Calendar",
                  description: "A smart, easy-to-use calendar that manages your entire schedule"
                },
                {
                  icon: (
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  ),
                  title: "Get Discovered",
                  description: "Attract new clients through the Vierla marketplace"
                },
                {
                  icon: (
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  ),
                  title: "No-Show Protection",
                  description: "Secure your income with deposit and cancellation policies"
                },
                {
                  icon: (
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  ),
                  title: "Built-in Marketing",
                  description: "Use simple tools to send promotions and keep clients coming back"
                }
              ].map((benefit, index) => (
                <div
                  key={index}
                  className="bg-white/15 backdrop-blur-md rounded-2xl p-8 shadow-xl border border-white/25 transform hover:scale-105 transition-all duration-300"
                >
                  <div className="w-16 h-16 mx-auto mb-6 rounded-2xl flex items-center justify-center" style={{backgroundColor: 'rgba(184, 149, 106, 0.2)'}}>
                    <div style={{color: '#B8956A'}}>
                      {benefit.icon}
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4 drop-shadow-sm" style={{fontFamily: 'Playfair Display, serif'}}>
                    {benefit.title}
                  </h3>
                  <p className="text-white/80 leading-relaxed drop-shadow-sm">
                    {benefit.description}
                  </p>
                </div>
              ))}
            </div>

            {/* Coming Soon Section */}
            <div className="bg-white/20 backdrop-blur-md rounded-3xl p-12 shadow-2xl border border-white/30">
              <h3 className="text-4xl font-black text-white mb-6 drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>Coming Soon</h3>
              <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto drop-shadow-sm">
                We&apos;re building something special for beauty professionals. Join our waitlist to be the first to know when we launch.
              </p>

              {/* App Store Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                <div className="flex items-center px-6 py-3 bg-black/50 rounded-xl border border-white/20 cursor-not-allowed opacity-75">
                  <svg className="w-8 h-8 mr-3" viewBox="0 0 24 24" fill="white">
                    <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                  </svg>
                  <div className="text-left">
                    <div className="text-xs text-gray-300">Coming Soon to</div>
                    <div className="text-lg font-semibold text-white">Apple App Store</div>
                  </div>
                </div>

                <div className="flex items-center px-6 py-3 bg-black/50 rounded-xl border border-white/20 cursor-not-allowed opacity-75">
                  <svg className="w-8 h-8 mr-3" viewBox="0 0 24 24" fill="white">
                    <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
                  </svg>
                  <div className="text-left">
                    <div className="text-xs text-gray-300">Coming Soon to</div>
                    <div className="text-lg font-semibold text-white">Google Play Store</div>
                  </div>
                </div>
              </div>

              {/* Alternative CTA */}
              <div className="border-t border-white/20 pt-8">
                <p className="text-white/70 mb-4">Want to be notified when we launch? Join our waitlist:</p>
                <Link
                  href="/apply"
                  className="inline-flex items-center px-6 py-3 rounded-full border-2 text-white hover:bg-white/10 transition-all duration-300"
                  style={{borderColor: '#B8956A', color: '#B8956A'}}
                >
                  Apply to Join Vierla
                  <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
            </div>

          </div>
        </div>
      </main>

      <Footer />


    </div>
  )
}
