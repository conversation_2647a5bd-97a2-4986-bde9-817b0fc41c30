"use client"
import { useEffect } from 'react'
import { usePathname } from 'next/navigation'
import Script from 'next/script'

// Analytics configuration
const ANALYTICS_CONFIG = {
  // Replace with your actual analytics IDs
  googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID || 'G-XXXXXXXXXX',
  facebookPixelId: process.env.NEXT_PUBLIC_FB_PIXEL_ID || '000000000000000',
  hotjarId: process.env.NEXT_PUBLIC_HOTJAR_ID || '0000000',
  enabled: process.env.NODE_ENV === 'production'
}

// Google Analytics with error handling
export function GoogleAnalytics() {
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Initialize gtag function
      window.gtag = window.gtag || function(...args: any[]) {
        (window.gtag.q = window.gtag.q || []).push(args)
      }
      window.gtag.l = +new Date()

      // Set default consent to denied
      window.gtag('consent', 'default', {
        'analytics_storage': 'denied',
        'ad_storage': 'denied',
        'ad_user_data': 'denied',
        'ad_personalization': 'denied'
      })
    }
  }, [])

  if (!ANALYTICS_CONFIG.enabled || !ANALYTICS_CONFIG.googleAnalyticsId) {
    return null
  }

  return (
    <>
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${ANALYTICS_CONFIG.googleAnalyticsId}`}
        strategy="afterInteractive"
        onLoad={() => {
          console.log('Google Analytics loaded successfully')
        }}
        onError={(e) => {
          console.log('Google Analytics failed to load:', e)
        }}
      />
      <Script
        id="google-analytics-config"
        strategy="afterInteractive"
        onLoad={() => {
          console.log('Google Analytics configured successfully')
        }}
        onError={(e) => {
          console.log('Google Analytics config failed:', e)
        }}
      >
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${ANALYTICS_CONFIG.googleAnalyticsId}', {
            page_title: document.title,
            page_location: window.location.href,
            custom_map: {
              'custom_parameter_1': 'beauty_service_interest'
            }
          });
        `}
      </Script>
    </>
  )
}

// Facebook Pixel with error handling (disabled by default to prevent console errors)
export function FacebookPixel() {
  // Temporarily disabled to prevent console errors
  // Enable only when you have a valid Facebook Pixel ID
  if (!ANALYTICS_CONFIG.enabled || !ANALYTICS_CONFIG.facebookPixelId || ANALYTICS_CONFIG.facebookPixelId === '000000000000000') {
    return null
  }

  return (
    <>
      <Script
        id="facebook-pixel"
        strategy="afterInteractive"
        onLoad={() => {
          console.log('Facebook Pixel loaded successfully')
        }}
        onError={(e) => {
          console.log('Facebook Pixel failed to load:', e)
        }}
      >
        {`
          !function(f,b,e,v,n,t,s)
          {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
          n.callMethod.apply(n,arguments):n.queue.push(arguments)};
          if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
          n.queue=[];t=b.createElement(e);t.async=!0;
          t.src=v;s=b.getElementsByTagName(e)[0];
          s.parentNode.insertBefore(t,s)}(window, document,'script',
          'https://connect.facebook.net/en_US/fbevents.js');

          try {
            fbq('init', '${ANALYTICS_CONFIG.facebookPixelId}');
            fbq('track', 'PageView');
          } catch (error) {
            console.log('Facebook Pixel initialization error:', error);
          }
        `}
      </Script>
    </>
  )
}

// Hotjar with error handling (disabled by default)
export function Hotjar() {
  // Temporarily disabled to prevent console errors
  // Enable only when you have a valid Hotjar ID
  if (!ANALYTICS_CONFIG.enabled || !ANALYTICS_CONFIG.hotjarId || ANALYTICS_CONFIG.hotjarId === '0000000') {
    return null
  }

  return (
    <Script
      id="hotjar"
      strategy="afterInteractive"
      onLoad={() => {
        console.log('Hotjar loaded successfully')
      }}
      onError={(e) => {
        console.log('Hotjar failed to load:', e)
      }}
    >
      {`
        (function(h,o,t,j,a,r){
          h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
          h._hjSettings={hjid:${ANALYTICS_CONFIG.hotjarId},hjsv:6};
          a=o.getElementsByTagName('head')[0];
          r=o.createElement('script');r.async=1;
          r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
          a.appendChild(r);
        })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
      `}
    </Script>
  )
}

// Page view tracking
export function usePageTracking() {
  const pathname = usePathname()

  useEffect(() => {
    if (!ANALYTICS_CONFIG.enabled) return

    const url = pathname

    // Google Analytics page view
    if ((window as any).gtag) {
      ;(window as any).gtag('config', ANALYTICS_CONFIG.googleAnalyticsId, {
        page_path: url,
        page_title: document.title
      })
    }

    // Facebook Pixel page view
    if ((window as any).fbq) {
      ;(window as any).fbq('track', 'PageView')
    }

    // Custom analytics storage
    const pageViews = JSON.parse(localStorage.getItem('vierla-page-views') || '[]')
    pageViews.push({
      url,
      title: document.title,
      timestamp: Date.now(),
      referrer: document.referrer,
      userAgent: navigator.userAgent
    })

    // Keep only last 100 page views
    if (pageViews.length > 100) {
      pageViews.splice(0, pageViews.length - 100)
    }

    localStorage.setItem('vierla-page-views', JSON.stringify(pageViews))
  }, [pathname])
}

// Event tracking utilities
export const trackEvent = (eventName: string, parameters?: Record<string, any>) => {
  if (!ANALYTICS_CONFIG.enabled) return

  // Google Analytics event
  if ((window as any).gtag) {
    ;(window as any).gtag('event', eventName, {
      event_category: 'engagement',
      event_label: parameters?.label || '',
      value: parameters?.value || 0,
      ...parameters
    })
  }

  // Facebook Pixel event
  if ((window as any).fbq) {
    ;(window as any).fbq('track', eventName, parameters)
  }

  // Custom event storage
  const events = JSON.parse(localStorage.getItem('vierla-events') || '[]')
  events.push({
    name: eventName,
    parameters,
    timestamp: Date.now(),
    url: window.location.pathname
  })

  // Keep only last 200 events
  if (events.length > 200) {
    events.splice(0, events.length - 200)
  }

  localStorage.setItem('vierla-events', JSON.stringify(events))
}

// Conversion tracking
export const trackConversion = (conversionType: string, value?: number) => {
  trackEvent('conversion', {
    conversion_type: conversionType,
    value: value || 0,
    currency: 'USD'
  })
}

// Email signup tracking
export const trackEmailSignup = (source: string) => {
  trackEvent('email_signup', {
    source,
    event_category: 'lead_generation'
  })
  
  trackConversion('email_signup')
}

// Contact form tracking
export const trackContactForm = (formType: string) => {
  trackEvent('contact_form_submit', {
    form_type: formType,
    event_category: 'lead_generation'
  })
  
  trackConversion('contact_form')
}

// Service interest tracking
export const trackServiceInterest = (service: string) => {
  trackEvent('service_interest', {
    service_type: service,
    event_category: 'engagement'
  })
}

// Professional interest tracking
export const trackProfessionalInterest = () => {
  trackEvent('professional_interest', {
    event_category: 'business_development'
  })
}

// Main Analytics component
export function Analytics() {
  usePageTracking()

  return (
    <>
      <GoogleAnalytics />
      <FacebookPixel />
      <Hotjar />
    </>
  )
}

// Utility to get analytics data (for debugging)
export function getAnalyticsData() {
  if (typeof window === 'undefined') return null

  return {
    pageViews: JSON.parse(localStorage.getItem('vierla-page-views') || '[]'),
    events: JSON.parse(localStorage.getItem('vierla-events') || '[]'),
    performance: JSON.parse(localStorage.getItem('vierla-performance') || '[]'),
    customMetrics: JSON.parse(localStorage.getItem('vierla-custom-metrics') || '[]')
  }
}
