"use client"
import { useState } from 'react'
import Link from "next/link"
import { HeartIcon } from "@/components/ui/heart-icon"
import { trackEvent } from "@/components/analytics"
import { Footer } from "@/components/ui/footer"
import { submitProfessionalApplication } from "@/lib/api"

interface ApplicationData {
  // Personal Information
  firstName: string
  lastName: string
  email: string
  phone: string

  // Professional Information
  businessName: string
  services: string[]
  experience: string
  certifications: string[]

  // Location & Availability
  serviceAreas: string[]
  availability: string[]
  travelRadius: string

  // Business Details
  insurance: boolean
  license: string
  portfolio: string
  rates: string

  // Additional Information
  motivation: string
  references: string
}

const serviceOptions = [
  'Hair Styling', 'Hair Cutting', 'Hair Coloring', 'Blowouts',
  'Makeup Application', 'Bridal Makeup', 'Special Event Makeup',
  'Manicures', 'Pedicures', 'Nail Art', 'Gel Polish',
  'Eyebrow Shaping', 'Eyebrow Threading', 'Eyebrow Tinting',
  'Eyelash Extensions', 'Lash Lifts', '<PERSON><PERSON>',
  'Braiding', 'Box Braids', 'Cornrows', 'Protective Styles',
  'Loc Maintenance', 'Loc Styling', 'Retwisting',
  'Beard Trimming', 'Hot Towel Shaves', 'Men\'s Grooming'
]

// Validation functions
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const validatePhone = (phone: string): boolean => {
  // Remove all non-digit characters
  const cleanPhone = phone.replace(/\D/g, '')
  // Check if it's 10 or 11 digits (with or without country code)
  return cleanPhone.length === 10 || cleanPhone.length === 11
}

const formatPhone = (phone: string): string => {
  const cleanPhone = phone.replace(/\D/g, '')
  if (cleanPhone.length === 10) {
    return `(${cleanPhone.slice(0, 3)}) ${cleanPhone.slice(3, 6)}-${cleanPhone.slice(6)}`
  }
  return phone
}

export default function ApplicationPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState<ApplicationData>({
    firstName: '', lastName: '', email: '', phone: '',
    businessName: '', services: [], experience: '', certifications: [],
    serviceAreas: [], availability: [], travelRadius: '',
    insurance: false, license: '', portfolio: '', rates: '',
    motivation: '', references: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [submissionError, setSubmissionError] = useState<string | null>(null)
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({})

  const totalSteps = 5

  const handleInputChange = (field: keyof ApplicationData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleArrayChange = (field: keyof ApplicationData, value: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: checked
        ? [...(prev[field] as string[]), value]
        : (prev[field] as string[]).filter(item => item !== value)
    }))
  }

  const validateCurrentStep = (): { isValid: boolean; errors: string[] } => {
    const errors: string[] = []

    switch (currentStep) {
      case 1:
        if (!formData.firstName.trim()) errors.push('First name is required')
        if (!formData.lastName.trim()) errors.push('Last name is required')
        if (!formData.email.trim()) errors.push('Email is required')
        else if (!validateEmail(formData.email)) errors.push('Please enter a valid email address')
        if (!formData.phone.trim()) errors.push('Phone number is required')
        else if (!validatePhone(formData.phone)) errors.push('Please enter a valid phone number (10-11 digits)')
        break
      case 2:
        if (formData.services.length === 0) errors.push('Please select at least one service')
        if (!formData.experience) errors.push('Please select your years of experience')
        break
      case 3:
        if (!formData.license.trim()) errors.push('License information is required')
        break
      case 4:
        if (formData.serviceAreas.length === 0) errors.push('Please select at least one service area')
        if (!formData.travelRadius) errors.push('Please select your travel radius')
        if (!formData.rates.trim()) errors.push('Rate information is required')
        break
      case 5:
        if (!formData.motivation.trim()) errors.push('Please tell us why you want to join Vierla')
        break
    }

    return { isValid: errors.length === 0, errors }
  }

  const nextStep = () => {
    const validation = validateCurrentStep()
    if (!validation.isValid) {
      setFieldErrors(validation.errors.reduce((acc, error, index) => {
        acc[`step${currentStep}_error${index}`] = error
        return acc
      }, {} as Record<string, string>))
      alert(validation.errors.join('\n'))
      return
    }

    setFieldErrors({}) // Clear errors if validation passes
    if (currentStep < totalSteps) {
      setCurrentStep(prev => prev + 1)
      trackEvent('application_step_completed', {
        step: currentStep,
        event_category: 'professional_onboarding'
      })
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1)
    }
  }

  const handleSubmit = async () => {
    setSubmissionError(null); // Reset error on new submission attempt

    // --- START: Comprehensive Validation Logic ---
    const allRequiredFields: (keyof ApplicationData)[] = [
      'firstName', 'lastName', 'email', 'phone', 'services', 'experience', 'license', 'motivation'
    ];

    const fieldToStepMap: Record<string, number> = {
      firstName: 1, lastName: 1, email: 1, phone: 1,
      services: 2, experience: 2,
      license: 3,
      motivation: 5
    };
    
    const missingFields = allRequiredFields.filter(field => {
      const value = formData[field];
      if (Array.isArray(value)) {
        return value.length === 0;
      }
      return !value;
    });

    if (missingFields.length > 0) {
      const firstMissingField = missingFields[0];
      const stepWithError = fieldToStepMap[firstMissingField] || 1;
      setCurrentStep(stepWithError);
      setSubmissionError(`Please complete all required fields. The first missing field is '${firstMissingField}' on step ${stepWithError}.`);
      return;
    }
    // --- END: Comprehensive Validation Logic ---

    setIsSubmitting(true);

    try {
      // This console.log is for debugging the exact payload. Remove it for production.
      console.log('Submitting Application Data:', JSON.stringify(formData, null, 2));
      
      const response = await submitProfessionalApplication(formData);

      if (response.success) {
        setIsSubmitted(true);
        trackEvent('professional_application_submitted', {
          services_count: formData.services.length,
          experience_level: formData.experience,
          event_category: 'professional_onboarding'
        });
      } else {
        setSubmissionError(response.error || 'An unknown error occurred during submission.');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected network error occurred.';
      setSubmissionError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen relative overflow-hidden bg-transparent">
        <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
          <div className="p-8 md:p-12 text-center max-w-2xl">
            <div className="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center" style={{backgroundColor: 'rgba(244, 241, 232, 0.2)'}}>
              <svg className="w-10 h-10" style={{color: '#F4F1E8'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            
            <h1 className="text-3xl md:text-4xl font-bold text-white mb-4" style={{fontFamily: 'Playfair Display, serif'}}>
              Application Submitted!
            </h1>
            
            <p className="text-white/90 text-lg mb-6">
              Thank you for your interest in joining Vierla. We&apos;ve received your application and will review it within 3-5 business days.
            </p>
            
            <div className="bg-white/5 rounded-2xl p-6 mb-8">
              <h3 className="text-xl font-semibold text-white mb-4">What happens next?</h3>
              <div className="space-y-3 text-left">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 rounded-full flex items-center justify-center mt-0.5" style={{backgroundColor: '#B8956A'}}>
                    <span className="text-xs font-bold text-white">1</span>
                  </div>
                  <p className="text-white/80">Application review and background check</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 rounded-full flex items-center justify-center mt-0.5" style={{backgroundColor: '#B8956A'}}>
                    <span className="text-xs font-bold text-white">2</span>
                  </div>
                  <p className="text-white/80">Video interview with our team</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 rounded-full flex items-center justify-center mt-0.5" style={{backgroundColor: '#B8956A'}}>
                    <span className="text-xs font-bold text-white">3</span>
                  </div>
                  <p className="text-white/80">Platform onboarding and training</p>
                </div>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/"
                className="px-6 py-3 rounded-xl border border-white/30 text-white hover:bg-white/10 transition-all duration-300"
              >
                Return Home
              </Link>
              <Link
                href="/about"
                className="px-6 py-3 rounded-xl font-medium transition-all duration-300"
                style={{backgroundColor: '#B8956A', color: '#2D2A26'}}
              >
                Learn More About Vierla
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen relative overflow-hidden bg-transparent">
      {/* Header */}
      <header className="relative z-10 w-full px-4 py-6 border-b border-white/20" role="banner">
        <div className="max-w-6xl mx-auto">
          <nav className="flex items-center justify-between" role="navigation" aria-label="Main navigation">
            <Link
              href="/"
              className="flex items-center space-x-3 group"
              aria-label="Vierla - Go to homepage"
            >
              <div className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center shadow-lg border border-white/30 transform group-hover:scale-110 transition-all duration-300">
                <HeartIcon className="w-7 h-7 text-white" aria-hidden="true" />
              </div>
              <span className="text-3xl font-bold text-white drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>Vierla</span>
            </Link>
            <div className="flex items-center space-x-8" role="menubar">
              <Link
                href="/"
                className="text-white/90 hover:text-white transition-colors focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent rounded-md px-2 py-1"
                role="menuitem"
                aria-label="Go to homepage"
              >
                Home
              </Link>
              <Link
                href="/about"
                className="text-white/90 hover:text-white transition-colors focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent rounded-md px-2 py-1"
                role="menuitem"
                aria-label="About Vierla"
              >
                About
              </Link>
            </div>
          </nav>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 w-full px-4 py-8" role="main">
        <div className="max-w-4xl mx-auto">
          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
              <h1
                className="text-2xl md:text-3xl font-bold text-white mb-2 md:mb-0"
                style={{fontFamily: 'Playfair Display, serif'}}
                id="form-heading"
              >
                Join Vierla as a Professional
              </h1>
              <span
                className="text-white/80 text-sm md:text-base"
                aria-live="polite"
                aria-label={`Current progress: Step ${currentStep} of ${totalSteps}`}
              >
                Step {currentStep} of {totalSteps}
              </span>
            </div>
            <div
              className="w-full bg-white/20 rounded-full h-2"
              role="progressbar"
              aria-valuenow={currentStep}
              aria-valuemin={1}
              aria-valuemax={totalSteps}
              aria-label={`Application progress: ${Math.round((currentStep / totalSteps) * 100)}% complete`}
            >
              <div
                className="h-2 rounded-full transition-all duration-300"
                style={{
                  backgroundColor: '#B8956A',
                  width: `${(currentStep / totalSteps) * 100}%`
                }}
              />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/20">
            {/* Step Content */}
            {currentStep === 1 && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-6">Personal Information</h2>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">First Name *</label>
                    <input
                      type="text"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="Your first name"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Last Name *</label>
                    <input
                      type="text"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="Your last name"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Email Address *</label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Phone Number *</label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => {
                        const formatted = formatPhone(e.target.value)
                        handleInputChange('phone', formatted)
                      }}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="(*************"
                      required
                    />
                  </div>
                </div>
              </div>
            )}

            {currentStep === 2 && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-6">Professional Information</h2>
                <div className="space-y-6">
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Business Name</label>
                    <input
                      type="text"
                      value={formData.businessName}
                      onChange={(e) => handleInputChange('businessName', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="Your business name (if applicable)"
                    />
                  </div>
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Services You Offer *</label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-h-60 overflow-y-auto p-1">
                      {serviceOptions.map((service) => (
                        <label key={service} className="flex items-center space-x-2 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={formData.services.includes(service)}
                            onChange={(e) => handleArrayChange('services', service, e.target.checked)}
                            className="rounded border-white/30 bg-white/10 text-white focus:ring-white/50"
                          />
                          <span className="text-white/90 text-sm">{service}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Years of Experience *</label>
                    <select
                      value={formData.experience}
                      onChange={(e) => handleInputChange('experience', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white focus:outline-none focus:ring-2 focus:ring-white/50 appearance-none cursor-pointer"
                      style={{
                        backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`,
                        backgroundPosition: 'right 0.5rem center',
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: '1.5em 1.5em',
                        paddingRight: '2.5rem'
                      }}
                      required
                    >
                      <option value="" style={{backgroundColor: '#364035', color: '#F4F1E8'}}>Select experience level</option>
                      <option value="1-2" style={{backgroundColor: '#364035', color: '#F4F1E8'}}>1-2 years</option>
                      <option value="3-5" style={{backgroundColor: '#364035', color: '#F4F1E8'}}>3-5 years</option>
                      <option value="6-10" style={{backgroundColor: '#364035', color: '#F4F1E8'}}>6-10 years</option>
                      <option value="10+" style={{backgroundColor: '#364035', color: '#F4F1E8'}}>10+ years</option>
                    </select>
                  </div>
                </div>
              </div>
            )}

            {currentStep === 3 && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-6">Licenses & Certifications</h2>
                <div className="space-y-6">
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Professional License Number *</label>
                    <input
                      type="text"
                      value={formData.license}
                      onChange={(e) => handleInputChange('license', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="Your state license number"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Professional Insurance *</label>
                    <div className="space-y-3">
                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          name="insurance"
                          checked={formData.insurance === true}
                          onChange={() => handleInputChange('insurance', true)}
                          className="text-white focus:ring-white/50"
                        />
                        <span className="text-white/90">Yes, I have professional liability insurance</span>
                      </label>
                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          name="insurance"
                          checked={formData.insurance === false}
                          onChange={() => handleInputChange('insurance', false)}
                          className="text-white focus:ring-white/50"
                        />
                        <span className="text-white/90">No, I need help obtaining insurance</span>
                      </label>
                    </div>
                  </div>
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Portfolio/Website URL</label>
                    <input
                      type="url"
                      value={formData.portfolio}
                      onChange={(e) => handleInputChange('portfolio', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="https://your-portfolio.com"
                    />
                  </div>
                </div>
              </div>
            )}

            {currentStep === 4 && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-6">Service Areas & Availability</h2>
                <div className="space-y-6">
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Service Areas *</label>
                    <textarea
                      value={formData.serviceAreas.join(', ')}
                      onChange={(e) => handleInputChange('serviceAreas', e.target.value.split(/,\s*/))}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="List cities/areas you serve (e.g., Manhattan, Brooklyn, Queens)"
                      rows={3}
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Travel Radius *</label>
                    <select
                      value={formData.travelRadius}
                      onChange={(e) => handleInputChange('travelRadius', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white focus:outline-none focus:ring-2 focus:ring-white/50 appearance-none cursor-pointer"
                      style={{
                        backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`,
                        backgroundPosition: 'right 0.5rem center',
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: '1.5em 1.5em',
                        paddingRight: '2.5rem'
                      }}
                      required
                    >
                      <option value="" style={{backgroundColor: '#364035', color: '#F4F1E8'}}>Select travel radius</option>
                      <option value="10" style={{backgroundColor: '#364035', color: '#F4F1E8'}}>Within 10 miles</option>
                      <option value="25" style={{backgroundColor: '#364035', color: '#F4F1E8'}}>Within 25 miles</option>
                      <option value="50" style={{backgroundColor: '#364035', color: '#F4F1E8'}}>Within 50 miles</option>
                      <option value="unlimited" style={{backgroundColor: '#364035', color: '#F4F1E8'}}>No limit</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Typical Rate Range *</label>
                    <input
                      type="text"
                      value={formData.rates}
                      onChange={(e) => handleInputChange('rates', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="e.g., $75-150 per service"
                      required
                    />
                  </div>
                </div>
              </div>
            )}

            {currentStep === 5 && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-6">Final Details</h2>
                <div className="space-y-6">
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Why do you want to join Vierla? *</label>
                    <textarea
                      value={formData.motivation}
                      onChange={(e) => handleInputChange('motivation', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="Tell us about your goals and what attracts you to our platform..."
                      rows={4}
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Professional References</label>
                    <textarea
                      value={formData.references}
                      onChange={(e) => handleInputChange('references', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="List 2-3 professional references with contact information (optional)"
                      rows={3}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Error Message Display */}
            {submissionError && (
              <div className="mt-6 text-center text-red-300 bg-red-500/20 p-4 rounded-xl border border-red-500/30">
                <p><strong>Submission Failed</strong></p>
                <p className="text-sm mt-1">{submissionError}</p>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8">
              <button
                onClick={prevStep}
                disabled={currentStep === 1}
                className="px-6 py-3 rounded-xl border border-white/30 text-white hover:bg-white/10 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              
              {currentStep === totalSteps ? (
                <button
                  onClick={handleSubmit}
                  disabled={isSubmitting || !validateCurrentStep()}
                  className="px-8 py-3 rounded-xl font-medium transition-all duration-300 disabled:opacity-50"
                  style={{backgroundColor: '#B8956A', color: '#2D2A26'}}
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Application'}
                </button>
              ) : (
                <button
                  onClick={nextStep}
                  disabled={!validateCurrentStep()}
                  className="px-6 py-3 rounded-xl font-medium transition-all duration-300 disabled:opacity-50"
                  style={{backgroundColor: '#B8956A', color: '#2D2A26'}}
                >
                  Next
                </button>
              )}
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}