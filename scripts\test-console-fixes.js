// Test script to verify console error fixes
// Run this in the browser console to check implementations

console.log('🔍 Testing Vierla Console Error Fixes...\n');

// Test 1: Check if Service Worker is registered
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.getRegistrations().then(registrations => {
    if (registrations.length > 0) {
      console.log('✅ Service Worker registered successfully');
      console.log('   Scope:', registrations[0].scope);
    } else {
      console.log('❌ Service Worker not registered');
    }
  });
} else {
  console.log('❌ Service Worker not supported');
}

// Test 2: Check if Google Analytics is loaded properly
setTimeout(() => {
  if (typeof window.gtag === 'function') {
    console.log('✅ Google Analytics loaded successfully');
  } else {
    console.log('❌ Google Analytics not loaded');
  }
}, 2000);

// Test 3: Check if Facebook Pixel errors are resolved
setTimeout(() => {
  if (typeof window.fbq === 'function') {
    console.log('✅ Facebook Pixel loaded successfully');
  } else {
    console.log('ℹ️ Facebook Pixel not loaded (expected if disabled)');
  }
}, 2000);

// Test 4: Check CSP violations
const originalError = console.error;
let cspErrors = 0;
console.error = function(...args) {
  if (args.some(arg => typeof arg === 'string' && arg.includes('Content Security Policy'))) {
    cspErrors++;
  }
  originalError.apply(console, args);
};

setTimeout(() => {
  if (cspErrors === 0) {
    console.log('✅ No CSP violations detected');
  } else {
    console.log(`❌ ${cspErrors} CSP violations detected`);
  }
  console.error = originalError;
}, 3000);

// Test 5: Check if manifest is accessible
fetch('/manifest.json')
  .then(response => {
    if (response.ok) {
      console.log('✅ Manifest.json accessible');
      return response.json();
    } else {
      throw new Error('Manifest not found');
    }
  })
  .then(manifest => {
    console.log('   App name:', manifest.name);
    console.log('   Theme color:', manifest.theme_color);
  })
  .catch(error => {
    console.log('❌ Manifest.json error:', error.message);
  });

// Test 6: Check cookie consent visibility
setTimeout(() => {
  const cookieBanner = document.querySelector('[role="dialog"][aria-labelledby="cookie-banner-title"]');
  if (cookieBanner) {
    const styles = window.getComputedStyle(cookieBanner);
    const isVisible = styles.display !== 'none' && styles.visibility !== 'hidden';
    if (isVisible) {
      console.log('✅ Cookie consent banner is visible');
      console.log('   Background:', styles.background);
      console.log('   Text color:', styles.color);
    } else {
      console.log('ℹ️ Cookie consent banner hidden (user may have already consented)');
    }
  } else {
    console.log('❌ Cookie consent banner not found');
  }
}, 1000);

// Test 7: Performance metrics
setTimeout(() => {
  if ('performance' in window) {
    const navigation = performance.getEntriesByType('navigation')[0];
    if (navigation) {
      console.log('📊 Performance Metrics:');
      console.log(`   DOM Content Loaded: ${Math.round(navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart)}ms`);
      console.log(`   Load Complete: ${Math.round(navigation.loadEventEnd - navigation.loadEventStart)}ms`);
      console.log(`   First Paint: ${Math.round(navigation.responseEnd - navigation.requestStart)}ms`);
    }
  }
}, 4000);

console.log('\n⏳ Running tests... Results will appear above in 5 seconds.');
console.log('💡 Tip: Refresh the page and run this script again to test from a clean state.');
