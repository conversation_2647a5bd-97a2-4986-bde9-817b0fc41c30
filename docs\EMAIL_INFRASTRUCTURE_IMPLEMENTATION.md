# Email Infrastructure Implementation Guide
## Vierla Contact & Application Backend Systems

### Table of Contents
1. [Overview](#overview)
2. [Email Service Architecture](#email-service-architecture)
3. [Infrastructure Requirements](#infrastructure-requirements)
4. [Email Account Setup](#email-account-setup)
5. [Server Configuration](#server-configuration)
6. [Security Implementation](#security-implementation)
7. [Monitoring & Logging](#monitoring--logging)
8. [Deployment Checklist](#deployment-checklist)
9. [Troubleshooting](#troubleshooting)

---

## Overview

This document outlines the complete infrastructure implementation required for Vierla's email backend systems, specifically for:
- **Contact Us Form** → `<EMAIL>`
- **Professional Application Form** → `<EMAIL>`

### Current Implementation Status
- ✅ Backend API routes created (`/api/contact`, `/api/professional-application`)
- ✅ Email service utility implemented (`lib/email.ts`)
- ✅ Email templates designed
- ⚠️ **REQUIRES**: Production email service configuration
- ⚠️ **REQUIRES**: Email account setup and DNS configuration

---

## Email Service Architecture

### Recommended Email Service Providers

#### Option 1: SendGrid (Recommended)
- **Cost**: 100,000 emails/month free tier, then $14.95/month
- **Reliability**: 99.9% uptime SLA
- **Features**: Templates, analytics, deliverability optimization
- **Setup Complexity**: Low

#### Option 2: Amazon SES
- **Cost**: $0.10 per 1,000 emails
- **Reliability**: 99.9% uptime SLA
- **Features**: High deliverability, AWS integration
- **Setup Complexity**: Medium

#### Option 3: Mailgun
- **Cost**: 5,000 emails/month free, then $35/month
- **Reliability**: 99.9% uptime SLA
- **Features**: Advanced analytics, A/B testing
- **Setup Complexity**: Low

### Email Flow Architecture
```
Contact Form → API Route → Email Service → <EMAIL>
Application Form → API Route → Email Service → <EMAIL>
```

---

## Infrastructure Requirements

### Server Requirements
- **Node.js**: 18+ (Current: ✅ Implemented)
- **Memory**: Minimum 512MB RAM for email processing
- **Storage**: 1GB for email logs and temporary files
- **Network**: Outbound SMTP access (ports 587, 465, 25)

### Dependencies
```json
{
  "nodemailer": "^6.9.8",
  "@types/nodemailer": "^6.4.14"
}
```
**Status**: ✅ Installed

### Environment Variables Required
```bash
# Email Service Configuration
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASS=your_sendgrid_api_key
FROM_EMAIL=<EMAIL>

# Email Routing
SUPPORT_EMAIL=<EMAIL>
APPLY_EMAIL=<EMAIL>

# Security
EMAIL_RATE_LIMIT=10  # emails per minute per IP
EMAIL_ENCRYPTION_KEY=your_encryption_key
```

---

## Email Account Setup

### Domain Configuration (vierla.com)

#### 1. DNS Records Required
```dns
# MX Records (for receiving emails)
MX 10 mail.vierla.com
MX 20 mail2.vierla.com

# SPF Record (for sending authentication)
TXT "v=spf1 include:sendgrid.net ~all"

# DKIM Record (for email signing)
TXT "k=rsa; p=YOUR_DKIM_PUBLIC_KEY"

# DMARC Record (for email policy)
TXT "v=DMARC1; p=quarantine; rua=mailto:<EMAIL>"
```

#### 2. Email Account Creation

**<EMAIL> Setup:**
- **Purpose**: Customer support, general inquiries, contact form submissions
- **Mailbox Size**: 10GB minimum
- **Auto-responder**: "Thank you for contacting Vierla. We'll respond within 24 hours."
- **Forwarding**: Optional to team members
- **Retention**: 2 years for compliance

**<EMAIL> Setup:**
- **Purpose**: Professional applications, recruitment inquiries
- **Mailbox Size**: 25GB minimum (for attachments)
- **Auto-responder**: "Thank you for your application. We'll review and respond within 3-5 business days."
- **Forwarding**: To HR team and hiring managers
- **Retention**: 7 years for legal compliance

#### 3. Email Client Configuration
```
IMAP Settings:
- Server: mail.vierla.com
- Port: 993 (SSL)
- Security: SSL/TLS

SMTP Settings:
- Server: mail.vierla.com  
- Port: 587 (STARTTLS)
- Security: STARTTLS
- Authentication: Required
```

---

## Server Configuration

### 1. Email Service Setup (SendGrid Example)

#### SendGrid Account Setup
1. Create SendGrid account at sendgrid.com
2. Verify domain ownership for vierla.com
3. Generate API key with "Mail Send" permissions
4. Configure sender authentication

#### API Key Generation
```bash
# In SendGrid Dashboard:
# Settings → API Keys → Create API Key
# Name: "Vierla Production Email"
# Permissions: "Mail Send" (Full Access)
# Copy the generated key to SMTP_PASS environment variable
```

### 2. Server Environment Configuration

#### Production Environment (.env.production)
```bash
# Email Configuration
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASS=SG.your_actual_api_key_here
FROM_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>
APPLY_EMAIL=<EMAIL>

# Rate Limiting
EMAIL_RATE_LIMIT=10
EMAIL_DAILY_LIMIT=1000

# Monitoring
EMAIL_LOG_LEVEL=info
EMAIL_WEBHOOK_URL=https://api.vierla.com/webhooks/email-status
```

#### Development Environment (.env.local)
```bash
# For testing - use Mailtrap or similar
SMTP_HOST=smtp.mailtrap.io
SMTP_PORT=2525
SMTP_USER=your_mailtrap_user
SMTP_PASS=your_mailtrap_pass
FROM_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>
APPLY_EMAIL=<EMAIL>
```

### 3. Firewall Configuration
```bash
# Allow outbound SMTP
sudo ufw allow out 587/tcp
sudo ufw allow out 465/tcp
sudo ufw allow out 25/tcp

# Allow inbound HTTP/HTTPS for webhooks
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
```

---

## Security Implementation

### 1. Email Validation & Sanitization
```javascript
// Already implemented in lib/email.ts
- Email format validation
- HTML sanitization
- Rate limiting per IP
- CSRF protection
```

### 2. API Security
```javascript
// Implement in API routes
- Request rate limiting
- Input validation
- SQL injection prevention
- XSS protection
```

### 3. Email Content Security
```javascript
// Email template security
- HTML sanitization
- No executable content
- Safe image handling
- Link validation
```

### 4. Access Control
```bash
# Email account access
- 2FA enabled for all email accounts
- Strong passwords (16+ characters)
- Regular password rotation (90 days)
- Access logging enabled
```

---

## Monitoring & Logging

### 1. Email Delivery Monitoring
```javascript
// Implement webhook handlers for:
- Email delivery confirmations
- Bounce notifications
- Spam complaints
- Unsubscribe requests
```

### 2. Application Logging
```javascript
// Log levels and events:
- INFO: Successful email sends
- WARN: Delivery delays
- ERROR: Send failures
- DEBUG: SMTP connection details
```

### 3. Metrics to Track
- Email delivery rate (target: >95%)
- Average delivery time (target: <30 seconds)
- Bounce rate (target: <5%)
- Spam complaint rate (target: <0.1%)

### 4. Alerting Setup
```bash
# Set up alerts for:
- Email send failures (>5 in 10 minutes)
- High bounce rate (>10% in 1 hour)
- SMTP connection failures
- API rate limit exceeded
```

---

## Deployment Checklist

### Pre-Deployment
- [ ] Domain DNS records configured
- [ ] Email accounts created and tested
- [ ] SendGrid account verified
- [ ] API keys generated and secured
- [ ] Environment variables configured
- [ ] SSL certificates installed

### Deployment Steps
1. [ ] Deploy application with email functionality
2. [ ] Test email sending from production
3. [ ] Verify email delivery to both accounts
4. [ ] Test auto-responders
5. [ ] Configure monitoring and alerts
6. [ ] Document access credentials securely

### Post-Deployment
- [ ] Monitor email delivery for 24 hours
- [ ] Test contact form submissions
- [ ] Test application form submissions
- [ ] Verify email forwarding rules
- [ ] Check spam folder delivery
- [ ] Update team on new email addresses

---

## Troubleshooting

### Common Issues

#### 1. Emails Not Sending
```bash
# Check SMTP credentials
# Verify firewall rules
# Check SendGrid account status
# Review application logs
```

#### 2. Emails Going to Spam
```bash
# Verify SPF/DKIM/DMARC records
# Check sender reputation
# Review email content for spam triggers
# Warm up IP address gradually
```

#### 3. High Bounce Rate
```bash
# Validate email addresses before sending
# Clean email list regularly
# Monitor bounce reasons
# Implement double opt-in for subscriptions
```

### Support Contacts
- **SendGrid Support**: <EMAIL>
- **DNS Provider**: [Your DNS provider support]
- **Hosting Provider**: [Your hosting provider support]

---

## Cost Estimation

### Monthly Costs (Estimated)
- **SendGrid**: $15-50/month (depending on volume)
- **Email Hosting**: $10-25/month per mailbox
- **Monitoring Tools**: $20-50/month
- **SSL Certificates**: $0-100/year
- **Total**: ~$45-125/month

### Implementation Time
- **Setup**: 2-3 days
- **Testing**: 1-2 days  
- **Documentation**: 1 day
- **Total**: 4-6 days

---

*Last Updated: January 2025*
*Document Version: 1.0*
