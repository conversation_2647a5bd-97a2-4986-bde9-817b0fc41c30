services:
  vierla-web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vierla-web
    restart: unless-stopped
    ports:
      - "3000:3000"
    networks:
      - services
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
    secrets:
      - sendgrid_api_key
    healthcheck:
      test: ["CMD", "curl", "--silent", "--fail", "http://127.0.0.1:3000/api/health"]
      interval: 30s
      timeout: 10s
      start_period: 30s
      retries: 3
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"

networks:
  services:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

secrets:
  sendgrid_api_key:
    file: /opt/secrets/sendgrid_api_key